package com.caidaocloud.message.test;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.announcement.dto.AnnouncementActivePageDto;

import com.caidaocloud.message.service.application.announcement.dto.AnnouncementDto;
import com.caidaocloud.message.service.application.announcement.service.AnnouncementApplicationService;
import com.caidaocloud.message.service.application.announcement.service.AnnouncementStatusService;
import com.caidaocloud.message.service.application.announcement.service.PublishedAnnouncementService;
import com.caidaocloud.message.service.application.feign.MasterdataFeignClientV2;
import com.caidaocloud.oss.service.OssService;
import com.caidaocloud.message.service.domain.announcement.entity.Announcement;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementContent;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementViewRecord;
import com.caidaocloud.message.service.domain.announcement.enums.ReceiveType;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementContentRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementViewRecordRepository;
import com.caidaocloud.message.service.interfaces.dto.base.UserDetailVo;
import com.caidaocloud.message.service.interfaces.vo.announcement.PublishedAnnouncementDetailVo;
import com.caidaocloud.message.service.interfaces.vo.announcement.PublishedAnnouncementPageVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.List;

/**
 * 已发布公告功能测试类
 *
 * <AUTHOR> Zhou
 * @date 2025/7/11
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class PublishedAnnouncementTest {

        @Resource
        private PublishedAnnouncementService publishedAnnouncementService;

        @Resource
        private AnnouncementApplicationService announcementApplicationService;

        @Resource
        private AnnouncementRepository announcementRepository;

        @Resource
        private AnnouncementContentRepository announcementContentRepository;

        @Resource
        private AnnouncementViewRecordRepository announcementViewRecordRepository;

        @Resource
        private AnnouncementStatusService announcementStatusService;

        @MockBean
        private MasterdataFeignClientV2 masterdataFeignClientV2;

        @MockBean
        private OssService ossService;

        private String testUserId = "1923451746924549";

        private String orgId = "2122527599229757";
        private String orgPid = "2122526552505149";
        private String empId = "2047437853497344";

        @Before
        public void setUp() {
                // 设置测试用户信息
                SecurityUserInfo userInfo = new SecurityUserInfo();
                userInfo.setTenantId("11");
                userInfo.setUserId(1923451746924549L);
                SecurityUserUtil.setSecurityUserInfo(userInfo);

                // 设置MasterdataFeignClientV2的mock返回数据
                setupMasterdataFeignClientMock();

                // 设置OSS服务的mock返回数据
                setupOssServiceMock();
        }

        /**
         * 设置MasterdataFeignClientV2的mock数据
         */
        private void setupMasterdataFeignClientMock() {
                // 创建mock用户详情数据 - 使用全参数构造方法
                UserDetailVo mockUserDetail = new UserDetailVo(
                                "http://test-pc-logo.png", // pcTenantLogo
                                "http://test-app-logo.png", // appTenantLogo
                                "测试公司", // companyName
                                "TEST_COMPANY", // companyCode
                                "hr,message", // licencedModules
                                empId, // empId
                                "测试用户", // name
                                orgId, // orgId
                                "T001", // workno
                                "TestUser", // enName
                                "测试岗位", // postName
                                "http://test-avatar.png" // headPortrait
                );

                // mock loadCurrentUserDetail方法
                Result<UserDetailVo> mockResult = Result.ok(mockUserDetail);

                Mockito.when(masterdataFeignClientV2.loadCurrentUserDetail()).thenReturn(mockResult);
        }

        /**
         * 设置OSS服务的mock数据
         */
        private void setupOssServiceMock() {
                try {
                        // mock OSS下载方法，返回测试文件流
                        java.io.ByteArrayInputStream mockInputStream = new java.io.ByteArrayInputStream(
                                        "这是测试附件内容".getBytes("UTF-8"));
                        Mockito.when(ossService.getInputStream(Mockito.anyString())).thenReturn(mockInputStream);
                } catch (Exception e) {
                        log.error("设置OSS服务mock失败", e);
                }
        }

        /**
         * 测试分页查询已发布公告
         */
        @Test
        public void testGetPublishedAnnouncementPage() {
                log.info("开始测试分页查询已发布公告");

                // 1. 创建并发布测试公告
                Announcement announcement = createAndPublishTestAnnouncement("测试已发布公告分页查询");

                // 2. 创建查询条件
                AnnouncementActivePageDto dto = new AnnouncementActivePageDto();
                dto.setPageNo(1);
                dto.setPageSize(10);

                // 3. 执行分页查询
                PageResult<PublishedAnnouncementPageVo> result = publishedAnnouncementService
                                .getPublishedAnnouncementPage(dto);

                // 4. 验证结果
                Assert.assertNotNull("分页结果不应该为空", result);
                Assert.assertNotNull("分页数据不应该为空", result.getItems());
                Assert.assertTrue("应该有至少一条数据", result.getItems().size() > 0);

                // 验证第一条数据
                PublishedAnnouncementPageVo firstItem = result.getItems().get(0);
                Assert.assertNotNull("公告ID不应该为空", firstItem.getBid());
                Assert.assertNotNull("公告名称不应该为空", firstItem.getName());
                Assert.assertNotNull("发布时间不应该为空", firstItem.getReleaseTime());
                Assert.assertFalse("初始状态应该未查看", firstItem.getIsViewed());

                log.info("分页查询已发布公告测试通过，查询到{}条数据", result.getItems().size());
        }

        /**
         * 测试查询已发布公告详情
         */
        @Test
        public void testGetPublishedAnnouncementDetail() {
                log.info("开始测试查询已发布公告详情");

                // 1. 创建并发布测试公告
                Announcement announcement = createAndPublishTestAnnouncement("测试已发布公告详情查询");
                String announcementId = announcement.getBid();

                // 2. 查询公告详情
                PublishedAnnouncementDetailVo result = publishedAnnouncementService
                                .getPublishedAnnouncementDetail(announcementId);

                // 3. 验证结果
                Assert.assertNotNull("公告详情不应该为空", result);
                Assert.assertEquals("公告ID应该一致", announcementId, result.getBid());
                Assert.assertNotNull("公告名称不应该为空", result.getName());
                Assert.assertNotNull("公告内容不应该为空", result.getContent());
                Assert.assertNotNull("发布时间不应该为空", result.getReleaseTime());

                // 4. 验证查看记录已创建
                AnnouncementViewRecord viewRecord = announcementViewRecordRepository
                                .findByAnnouncementIdAndUserId(announcementId, testUserId);
                Assert.assertNotNull("查看记录应该已创建", viewRecord);
                Assert.assertEquals("查看记录的公告ID应该一致", announcementId, viewRecord.getAnnouncementId());
                Assert.assertEquals("查看记录的用户ID应该一致", testUserId, viewRecord.getUserId());

                log.info("查询已发布公告详情测试通过");
        }

        /**
         * 测试查看记录功能
         */
        @Test
        public void testViewRecord() {
                log.info("开始测试查看记录功能");

                // 1. 创建并发布测试公告
                Announcement announcement = createAndPublishTestAnnouncement("测试查看记录功能");
                String announcementId = announcement.getBid();

                // 2. 第一次查询详情（创建查看记录）
                PublishedAnnouncementDetailVo firstResult = publishedAnnouncementService
                                .getPublishedAnnouncementDetail(announcementId);

                // 3. 分页查询验证查看状态
                AnnouncementActivePageDto dto = new AnnouncementActivePageDto();
                dto.setPageNo(1);
                dto.setPageSize(10);
                // dto.setName("测试查看记录功能");

                PageResult<PublishedAnnouncementPageVo> pageResult = publishedAnnouncementService
                                .getPublishedAnnouncementPage(dto);
                Assert.assertTrue("应该有查询结果", pageResult.getItems().size() > 0);

                PublishedAnnouncementPageVo pageItem = pageResult.getItems().stream()
                                .filter(item -> announcementId.equals(item.getBid()))
                                .findFirst()
                                .orElse(null);
                Assert.assertNotNull("应该能找到对应的公告", pageItem);
                Assert.assertTrue("分页查询中应该显示为已查看", pageItem.getIsViewed());

                // 4. 第二次查询详情（更新查看记录）
                PublishedAnnouncementDetailVo secondResult = publishedAnnouncementService
                                .getPublishedAnnouncementDetail(announcementId);

                log.info("查看记录功能测试通过");
        }

        /**
         * 测试查询条件过滤
         */
        @Test
        public void testQueryWithFilters() {
                log.info("开始测试查询条件过滤");

                // 1. 创建不同类型的测试公告
                Announcement announcement1 = createAndPublishTestAnnouncement("重要通知公告", true);

                Announcement announcement2 = createAndPublishTestAnnouncement("普通信息公告", false);

                // 2. 测试按名称查询
                AnnouncementActivePageDto nameDto = new AnnouncementActivePageDto();
                nameDto.setPageNo(1);
                nameDto.setPageSize(10);

                PageResult<PublishedAnnouncementPageVo> nameResult = publishedAnnouncementService
                                .getPublishedAnnouncementPage(nameDto);
                Assert.assertTrue("按名称查询应该有结果", nameResult.getItems().size() > 0);
                // Assert.assertTrue("查询结果应该包含关键字",
                // nameResult.getItems().stream().anyMatch(item ->
                // item.getName().contains("重要通知")));

                // 3. 测试按置顶状态查询
                AnnouncementActivePageDto topDto = new AnnouncementActivePageDto();
                topDto.setPageNo(1);
                topDto.setPageSize(10);

                PageResult<PublishedAnnouncementPageVo> topResult = publishedAnnouncementService
                                .getPublishedAnnouncementPage(topDto);
                Assert.assertTrue("按置顶查询应该有结果", topResult.getItems().size() > 0);
                Assert.assertTrue("查询结果应该都是置顶公告",
                                topResult.getItems().stream().anyMatch(PublishedAnnouncementPageVo::getIsTop));

                log.info("查询条件过滤测试通过");
        }

        /**
         * 测试不同接收类型的公告分页查询功能
         * 根据mock的用户信息验证4种receiveType的公告查询
         */
        @Test
        public void testPublishedAnnouncementPageWithDifferentReceiveTypes() {
                log.info("开始测试不同接收类型的公告分页查询功能");

                // 1. 创建所有人员类型的公告
                Announcement allPersonnelAnnouncement = createAndPublishAnnouncementWithReceiveType(
                                "所有人员公告", ReceiveType.ALL_PERSONNEL, null, null);

                // 2. 创建指定部门类型的公告 (使用mock用户的orgId)
                Announcement deptAnnouncement = createAndPublishAnnouncementWithReceiveType(
                                "指定部门公告", ReceiveType.DESIGNATED_DEPARTMENT,
                                Arrays.asList(orgId), null);

                // 3. 创建指定部门及下属部门类型的公告
                Announcement deptAndSubAnnouncement = createAndPublishAnnouncementWithReceiveType(
                                "指定部门及下属部门公告", ReceiveType.DESIGNATED_AND_SUB_DEPARTMENTS,
                                Arrays.asList(orgPid), null);

                // 4. 创建指定人员类型的公告 (使用mock用户的empId)
                EmpSimple empSimple = new EmpSimple();
                empSimple.setEmpId(empId);
                empSimple.setName("测试用户");
                Announcement personnelAnnouncement = createAndPublishAnnouncementWithReceiveType(
                                "指定人员公告", ReceiveType.DESIGNATED_PERSONNEL,
                                null, Arrays.asList(empSimple));

                // 5. 执行分页查询
                AnnouncementActivePageDto dto = new AnnouncementActivePageDto();
                dto.setPageNo(1);
                dto.setPageSize(20);

                PageResult<PublishedAnnouncementPageVo> result = publishedAnnouncementService
                                .getPublishedAnnouncementPage(dto);

                // 6. 验证查询结果
                Assert.assertNotNull("分页结果不应该为空", result);
                Assert.assertNotNull("分页数据不应该为空", result.getItems());
                Assert.assertTrue("应该查询到公告数据", result.getItems().size() > 0);

                // 验证能查询到所有类型的公告（因为当前用户符合所有条件）
                boolean hasAllPersonnel = result.getItems().stream()
                                .anyMatch(item -> item.getName().contains("所有人员公告"));
                boolean hasDept = result.getItems().stream()
                                .anyMatch(item -> item.getName().contains("指定部门公告"));
                boolean hasDeptAndSub = result.getItems().stream()
                                .anyMatch(item -> item.getName().contains("指定部门及下属部门公告"));
                boolean hasPersonnel = result.getItems().stream()
                                .anyMatch(item -> item.getName().contains("指定人员公告"));

                Assert.assertTrue("应该能查询到所有人员类型的公告", hasAllPersonnel);
                Assert.assertTrue("应该能查询到指定部门类型的公告", hasDept);
                Assert.assertTrue("应该能查询到指定部门及下属部门类型的公告", hasDeptAndSub);
                Assert.assertTrue("应该能查询到指定人员类型的公告", hasPersonnel);

                log.info("不同接收类型的公告分页查询功能测试通过，共查询到{}条公告", result.getItems().size());
        }

        /**
         * 测试不同接收类型的权限控制
         * 验证用户只能看到有权限的公告
         */
        @Test
        public void testReceiveTypePermissionControl() {
                log.info("开始测试不同接收类型的权限控制");

                // 1. 创建当前用户有权限的公告
                createAndPublishAnnouncementWithReceiveType("当前用户部门公告", ReceiveType.DESIGNATED_DEPARTMENT,
                                Arrays.asList(orgId), null);

                createAndPublishAnnouncementWithReceiveType("当前用户人员公告", ReceiveType.DESIGNATED_PERSONNEL,
                                null, Arrays.asList(createEmpSimple(empId, "测试用户")));

                // 2. 创建当前用户无权限的公告
                createAndPublishAnnouncementWithReceiveType("其他部门公告", ReceiveType.DESIGNATED_DEPARTMENT,
                                Arrays.asList("9999999999999999"), null);

                createAndPublishAnnouncementWithReceiveType("其他人员公告", ReceiveType.DESIGNATED_PERSONNEL,
                                null, Arrays.asList(createEmpSimple("9999999999999999", "其他用户")));

                // 3. 执行分页查询
                AnnouncementActivePageDto dto = new AnnouncementActivePageDto();
                dto.setPageNo(1);
                dto.setPageSize(20);

                PageResult<PublishedAnnouncementPageVo> result = publishedAnnouncementService
                                .getPublishedAnnouncementPage(dto);

                // 4. 验证权限控制
                Assert.assertNotNull("分页结果不应该为空", result);
                Assert.assertNotNull("分页数据不应该为空", result.getItems());

                // 验证能查询到有权限的公告
                boolean hasCurrentUserDept = result.getItems().stream()
                                .anyMatch(item -> item.getName().contains("当前用户部门公告"));
                boolean hasCurrentUserPersonnel = result.getItems().stream()
                                .anyMatch(item -> item.getName().contains("当前用户人员公告"));

                // 验证无法查询到无权限的公告
                boolean hasOtherDept = result.getItems().stream()
                                .anyMatch(item -> item.getName().contains("其他部门公告"));
                boolean hasOtherPersonnel = result.getItems().stream()
                                .anyMatch(item -> item.getName().contains("其他人员公告"));

                Assert.assertTrue("应该能查询到当前用户部门的公告", hasCurrentUserDept);
                Assert.assertTrue("应该能查询到当前用户人员的公告", hasCurrentUserPersonnel);
                Assert.assertFalse("不应该查询到其他部门的公告", hasOtherDept);
                Assert.assertFalse("不应该查询到其他人员的公告", hasOtherPersonnel);

                log.info("不同接收类型的权限控制测试通过");
        }

        /**
         * 创建EmpSimple对象的辅助方法
         */
        private EmpSimple createEmpSimple(String empId, String name) {
                EmpSimple empSimple = new EmpSimple();
                empSimple.setEmpId(empId);
                empSimple.setName(name);
                return empSimple;
        }

        private Announcement createAndPublishTestAnnouncement(String name) {
                return createAndPublishTestAnnouncement(name, false);
        }

        /**
         * 创建并发布测试公告
         */
        private Announcement createAndPublishTestAnnouncement(String name, boolean isTop) {
                // 创建公告DTO
                AnnouncementDto dto = createTestAnnouncementDto(name, isTop);

                // 使用Service创建公告
                String announcementId = announcementApplicationService.createAnnouncement(dto);

                // 发布公告
                announcementStatusService.publishAnnouncement(announcementId);

                return announcementRepository.selectById(announcementId, Announcement.identifier);
        }

        /**
         * 创建测试用的公告DTO
         */
        private AnnouncementDto createTestAnnouncementDto(String name, boolean isTop) {
                AnnouncementDto dto = new AnnouncementDto();
                dto.setName(name);
                dto.setType("4001");
                dto.setContent("这是测试公告的详细内容，用于验证已发布公告的查询功能。");
                dto.setReceiveType(ReceiveType.ALL_PERSONNEL);
                dto.setIsTop(isTop);
                dto.setDomain(Arrays.asList("test.domain.com"));
                dto.setReleaseTime(System.currentTimeMillis());
                dto.setEffectiveTime(System.currentTimeMillis() - 1000);
                dto.setExpiryTime(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L); // 7天后过期

                // 设置附件
                Attachment attachment = new Attachment();
                attachment.setNames(Lists.list("test.pdf"));
                attachment.setUrls(Lists.list("http://test.com/test.pdf"));
                dto.setAttachment(attachment);

                return dto;
        }

        /**
         * 创建测试用的公告DTO (默认不置顶)
         */
        private AnnouncementDto createTestAnnouncementDto(String name) {
                return createTestAnnouncementDto(name, false);
        }

        /**
         * 创建指定接收类型的公告并发布
         */
        private Announcement createAndPublishAnnouncementWithReceiveType(String name, ReceiveType receiveType,
                        List<String> receiveDeptList, List<EmpSimple> receiveEmpList) {
                AnnouncementDto dto = createTestAnnouncementDto(name);
                dto.setReceiveType(receiveType);
                dto.setReceiveDeptList(receiveDeptList);
                dto.setReceiveEmpList(receiveEmpList);

                // 使用Service创建公告
                String announcementId = announcementApplicationService.createAnnouncement(dto);

                // 发布公告
                announcementStatusService.publishAnnouncement(announcementId);

                return announcementRepository.selectById(announcementId, Announcement.identifier);
        }

        /**
         * 测试公告附件下载功能
         * 验证域名权限控制和附件下载
         */
        @Test
        public void testAnnouncementAttachmentDownload() {
                log.info("开始测试公告附件下载功能");

                // 1. 创建带附件的公告
                Announcement announcement = createAndPublishTestAnnouncement("带附件的测试公告");
                String announcementId = announcement.getBid();

                // 2. 获取附件ID（从公告内容中获取第一个附件）
                AnnouncementContent content = announcementContentRepository.findByAnnouncementId(announcementId);
                Assert.assertNotNull("公告内容不应该为空", content);
                Assert.assertNotNull("附件列表不应该为空", content.getAttachmentList());
                Assert.assertTrue("应该有附件", content.getAttachmentList().size() > 0);

                String attachmentId = content.getAttachmentList().get(0).getBid();

                // 3. 测试有效域名下载附件
                try {
                        java.io.InputStream inputStream = publishedAnnouncementService.downloadAnnouncementAttachment(
                                        announcementId, attachmentId, "test.domain.com");
                        Assert.assertNotNull("下载的文件流不应该为空", inputStream);

                        // 验证文件内容
                        byte[] buffer = new byte[1024];
                        int bytesRead = inputStream.read(buffer);
                        Assert.assertTrue("应该读取到文件内容", bytesRead > 0);

                        String content_str = new String(buffer, 0, bytesRead, "UTF-8");
                        Assert.assertEquals("文件内容应该匹配", "这是测试附件内容", content_str);

                        inputStream.close();
                        log.info("有效域名下载附件测试通过");
                } catch (Exception e) {
                        Assert.fail("有效域名下载附件应该成功：" + e.getMessage());
                }

                // 4. 测试无效域名下载附件
                try {
                        publishedAnnouncementService.downloadAnnouncementAttachment(
                                        announcementId, attachmentId, "invalid.domain.com");
                        Assert.fail("无效域名下载附件应该失败");
                } catch (RuntimeException e) {
                        Assert.assertTrue("应该是域名权限错误", e.getMessage().contains("当前域名无权限访问该公告附件"));
                        log.info("无效域名下载附件权限控制测试通过");
                }

                log.info("公告附件下载功能测试通过");
        }
}
