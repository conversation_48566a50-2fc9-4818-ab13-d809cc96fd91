package com.caidaocloud.message.test;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.message.service.MessageApplication;
import com.caidaocloud.message.service.application.announcement.dto.AnnouncementActivePageDto;
import com.caidaocloud.message.service.application.announcement.dto.PublishedAnnouncementPageDto;
import com.caidaocloud.message.service.application.announcement.service.AnnouncementStatusService;
import com.caidaocloud.message.service.application.announcement.service.PublishedAnnouncementService;
import com.caidaocloud.message.service.domain.announcement.entity.Announcement;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementContent;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementViewRecord;
import com.caidaocloud.message.service.domain.announcement.enums.AnnouncementStatus;
import com.caidaocloud.message.service.domain.announcement.enums.ReceiveType;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementContentRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementViewRecordRepository;
import com.caidaocloud.message.service.interfaces.vo.announcement.PublishedAnnouncementDetailVo;
import com.caidaocloud.message.service.interfaces.vo.announcement.PublishedAnnouncementPageVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * 已发布公告功能测试类
 *
 * <AUTHOR> Zhou
 * @date 2025/7/11
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = MessageApplication.class)
public class PublishedAnnouncementTest {

    @Resource
    private PublishedAnnouncementService publishedAnnouncementService;

    @Resource
    private AnnouncementRepository announcementRepository;

    @Resource
    private AnnouncementContentRepository announcementContentRepository;

    @Resource
    private AnnouncementViewRecordRepository announcementViewRecordRepository;

    @Resource
    private AnnouncementStatusService announcementStatusService;

    private String testUserId = "1001";

    @Before
    public void setUp() {
        // 设置测试用户信息
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(1923451746924549L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }

    /**
     * 测试分页查询已发布公告
     */
    @Test
    public void testGetPublishedAnnouncementPage() {
        log.info("开始测试分页查询已发布公告");

        // 1. 创建并发布测试公告
        Announcement announcement = createAndPublishTestAnnouncement("测试已发布公告分页查询");

        // 2. 创建查询条件
        AnnouncementActivePageDto dto = new AnnouncementActivePageDto();
        dto.setPageNo(1);
        dto.setPageSize(10);

        // 3. 执行分页查询
        PageResult<PublishedAnnouncementPageVo> result = publishedAnnouncementService.getPublishedAnnouncementPage(dto);

        // 4. 验证结果
        Assert.assertNotNull("分页结果不应该为空", result);
        Assert.assertNotNull("分页数据不应该为空", result.getItems());
        Assert.assertTrue("应该有至少一条数据", result.getItems().size() > 0);

        // 验证第一条数据
        PublishedAnnouncementPageVo firstItem = result.getItems().get(0);
        Assert.assertNotNull("公告ID不应该为空", firstItem.getBid());
        Assert.assertNotNull("公告名称不应该为空", firstItem.getName());
        Assert.assertNotNull("发布时间不应该为空", firstItem.getReleaseTime());
        Assert.assertFalse("初始状态应该未查看", firstItem.getIsViewed());

        log.info("分页查询已发布公告测试通过，查询到{}条数据", result.getItems().size());
    }

    /**
     * 测试查询已发布公告详情
     */
    @Test
    public void testGetPublishedAnnouncementDetail() {
        log.info("开始测试查询已发布公告详情");

        // 1. 创建并发布测试公告
        Announcement announcement = createAndPublishTestAnnouncement("测试已发布公告详情查询");
        String announcementId = announcement.getBid();

        // 2. 查询公告详情
        PublishedAnnouncementDetailVo result = publishedAnnouncementService.getPublishedAnnouncementDetail(announcementId);

        // 3. 验证结果
        Assert.assertNotNull("公告详情不应该为空", result);
        Assert.assertEquals("公告ID应该一致", announcementId, result.getBid());
        Assert.assertNotNull("公告名称不应该为空", result.getName());
        Assert.assertNotNull("公告内容不应该为空", result.getContent());
        Assert.assertNotNull("发布时间不应该为空", result.getReleaseTime());

        // 4. 验证查看记录已创建
        AnnouncementViewRecord viewRecord = announcementViewRecordRepository.findByAnnouncementIdAndUserId(announcementId, testUserId);
        Assert.assertNotNull("查看记录应该已创建", viewRecord);
        Assert.assertEquals("查看记录的公告ID应该一致", announcementId, viewRecord.getAnnouncementId());
        Assert.assertEquals("查看记录的用户ID应该一致", testUserId, viewRecord.getUserId());

        log.info("查询已发布公告详情测试通过");
    }

    /**
     * 测试查看记录功能
     */
    @Test
    public void testViewRecord() {
        log.info("开始测试查看记录功能");

        // 1. 创建并发布测试公告
        Announcement announcement = createAndPublishTestAnnouncement("测试查看记录功能");
        String announcementId = announcement.getBid();

        // 2. 第一次查询详情（创建查看记录）
        PublishedAnnouncementDetailVo firstResult = publishedAnnouncementService.getPublishedAnnouncementDetail(announcementId);

        // 3. 分页查询验证查看状态
        AnnouncementActivePageDto dto = new AnnouncementActivePageDto();
        dto.setPageNo(1);
        dto.setPageSize(10);
        // dto.setName("测试查看记录功能");

        PageResult<PublishedAnnouncementPageVo> pageResult = publishedAnnouncementService.getPublishedAnnouncementPage(dto);
        Assert.assertTrue("应该有查询结果", pageResult.getItems().size() > 0);

        PublishedAnnouncementPageVo pageItem = pageResult.getItems().stream()
                .filter(item -> announcementId.equals(item.getBid()))
                .findFirst()
                .orElse(null);
        Assert.assertNotNull("应该能找到对应的公告", pageItem);
        Assert.assertTrue("分页查询中应该显示为已查看", pageItem.getIsViewed());

        // 4. 第二次查询详情（更新查看记录）
        PublishedAnnouncementDetailVo secondResult = publishedAnnouncementService.getPublishedAnnouncementDetail(announcementId);

        log.info("查看记录功能测试通过");
    }

    /**
     * 测试查询条件过滤
     */
    @Test
    public void testQueryWithFilters() {
        log.info("开始测试查询条件过滤");

        // 1. 创建不同类型的测试公告
        Announcement announcement1 = createAndPublishTestAnnouncement("重要通知公告");
        announcement1.setIsTop(true);
        announcementRepository.updateById(announcement1);

        Announcement announcement2 = createAndPublishTestAnnouncement("普通信息公告");
        announcement2.setIsTop(false);
        announcementRepository.updateById(announcement2);

        // 2. 测试按名称查询
        AnnouncementActivePageDto nameDto = new AnnouncementActivePageDto();
        nameDto.setPageNo(1);
        nameDto.setPageSize(10);

        PageResult<PublishedAnnouncementPageVo> nameResult = publishedAnnouncementService.getPublishedAnnouncementPage(nameDto);
        Assert.assertTrue("按名称查询应该有结果", nameResult.getItems().size() > 0);
        Assert.assertTrue("查询结果应该包含关键字", 
                nameResult.getItems().stream().anyMatch(item -> item.getName().contains("重要通知")));

        // 3. 测试按置顶状态查询
        AnnouncementActivePageDto topDto = new AnnouncementActivePageDto();
        topDto.setPageNo(1);
        topDto.setPageSize(10);

        PageResult<PublishedAnnouncementPageVo> topResult = publishedAnnouncementService.getPublishedAnnouncementPage(topDto);
        Assert.assertTrue("按置顶查询应该有结果", topResult.getItems().size() > 0);
        Assert.assertTrue("查询结果应该都是置顶公告", 
                topResult.getItems().stream().allMatch(PublishedAnnouncementPageVo::getIsTop));

        log.info("查询条件过滤测试通过");
    }

    /**
     * 创建并发布测试公告
     */
    private Announcement createAndPublishTestAnnouncement(String name) {
        // 创建公告
        Announcement announcement = new Announcement();
        announcement.setName(name);

        DictSimple type = new DictSimple();
        type.setValue("test-type");
        announcement.setType(type);

        announcement.setReleaseTime(System.currentTimeMillis());
        announcement.setEffectiveTime(System.currentTimeMillis() - 1000);
        announcement.setExpiryTime(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L);
        announcement.setReceiveType(ReceiveType.ALL_PERSONNEL.toEnumSimple());
        announcement.setIsTop(false);
        announcement.setStatus(AnnouncementStatus.UNPUBLISHED.toEnumSimple());
        announcement.setDomain(Arrays.asList("test.domain.com"));

        announcement = announcementRepository.insert(announcement);

        // 创建公告内容
        AnnouncementContent content = new AnnouncementContent();
        content.setAnnouncementId(announcement.getBid());
        content.setContent("这是测试公告的详细内容，用于验证已发布公告的查询功能。");
        announcementContentRepository.insert(content);

        // 发布公告
        announcementStatusService.publishAnnouncement(announcement.getBid());

        return announcementRepository.selectById(announcement.getBid(), Announcement.identifier);
    }
}
