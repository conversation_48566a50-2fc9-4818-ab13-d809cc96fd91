package com.caidaocloud.message.service.application.announcement.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AbstractData;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.message.service.application.announcement.dto.AnnouncementActivePageDto;
import com.caidaocloud.message.service.application.announcement.dto.PublishedAnnouncementPageDto;
import com.caidaocloud.message.service.domain.announcement.entity.Announcement;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementActive;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementContent;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementViewRecord;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementActiveRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementContentRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementRepository;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementViewRecordRepository;
import com.caidaocloud.message.service.infrastructure.utils.HeaderUtil;
import com.caidaocloud.message.service.interfaces.vo.announcement.PublishedAnnouncementDetailVo;
import com.caidaocloud.message.service.interfaces.vo.announcement.PublishedAnnouncementPageVo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 已发布公告服务
 *
 * <AUTHOR> Zhou
 * @date 2025/7/11
 */
@Slf4j
@Service
public class PublishedAnnouncementService {

    @Resource
    private AnnouncementRepository announcementRepository;

    @Resource
    private AnnouncementActiveRepository announcementActiveRepository;

    @Resource
    private AnnouncementContentRepository announcementContentRepository;

    @Resource
    private AnnouncementViewRecordRepository announcementViewRecordRepository;

    @Value("${caidaocloud.msg.announcement.header:domain}")
    private String header;

    /**
     * 分页查询已发布的公告
     * @param dto 查询条件
     * @return 分页结果
     */
    public PageResult<PublishedAnnouncementPageVo> getPublishedAnnouncementPage(AnnouncementActivePageDto dto) {
        log.info("开始分页查询已发布公告，查询条件：{}", dto);

        // 查询已发布的公告
        PageResult<AnnouncementActive> announcementPage = announcementActiveRepository.selectPage(dto);
        
        if (announcementPage == null || announcementPage.getItems() == null || announcementPage.getItems().isEmpty()) {
            return new PageResult<>(null, dto.getPageNo(), dto.getPageSize(), 0);
        }

        List<AnnouncementViewRecord> record = announcementViewRecordRepository.listRecord(Sequences.sequence(announcementPage.getItems())
                .map(AbstractData::getBid)
                .toList());
        Set<String> set = Sequences.sequence(record).map(AnnouncementViewRecord::getAnnouncementId).toSet();
        // 转换为VO
        List<PublishedAnnouncementPageVo> voList = announcementPage.getItems().stream()
                .map(announcement -> {
                    PublishedAnnouncementPageVo vo = ObjectConverter.convert(announcement, PublishedAnnouncementPageVo.class);
                    vo.setIsViewed(set.contains(announcement.getBid()));
                    return vo;
                })
                .collect(Collectors.toList());

        return new PageResult<>(voList, announcementPage.getPageNo(), announcementPage.getPageSize(), announcementPage.getTotal());
    }

    /**
     * 查询已发布公告详情
     * @param announcementId 公告ID
     * @return 公告详情
     */
    @PaasTransactional
    public PublishedAnnouncementDetailVo getPublishedAnnouncementDetail(String announcementId) {
        log.info("开始查询已发布公告详情，公告ID：{}", announcementId);

        if (StringUtils.isEmpty(announcementId)) {
            throw new IllegalArgumentException("公告ID不能为空");
        }

        // 查询公告基本信息
        AnnouncementActive announcement = announcementActiveRepository.selectById(announcementId, Announcement.identifier);
        if (announcement == null) {
            throw new IllegalArgumentException("公告不存在");
        }

        // 查询公告内容
        AnnouncementContent content = announcementContentRepository.findByAnnouncementId(announcementId);

        // 获取当前用户ID
        String userId = String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId());

        // 记录查看记录
        recordViewRecord(announcementId, userId);

        // 转换为详情VO
        PublishedAnnouncementDetailVo detailVo = convertToDetailVo(announcement, content, userId);

        log.info("查询已发布公告详情完成，公告ID：{}", announcementId);
        return detailVo;
    }

    /**
     * 记录查看记录
     * @param announcementId 公告ID
     * @param userId 用户ID
     */
    private void recordViewRecord(String announcementId, String userId) {
        try {
            // 检查是否已存在查看记录
            AnnouncementViewRecord existingRecord = announcementViewRecordRepository.findByAnnouncementIdAndUserId(announcementId, userId);
            
            if (existingRecord == null) {
                // 创建新的查看记录
                AnnouncementViewRecord viewRecord = new AnnouncementViewRecord();
                viewRecord.setAnnouncementId(announcementId);
                viewRecord.setUserId(userId);
                
                announcementViewRecordRepository.insert(viewRecord);
                log.info("记录公告查看记录，公告ID：{}，用户ID：{}", announcementId, userId);
            }
        } catch (Exception e) {
            log.error("记录公告查看记录失败，公告ID：{}，用户ID：{}，错误：{}", announcementId, userId, e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 转换为分页VO
     * @param announcement 公告实体
     * @param userId 用户ID
     * @return 分页VO
     */
    private PublishedAnnouncementPageVo convertToPageVo(AnnouncementActive announcement, Set<String> viewRecordSet) {
        PublishedAnnouncementPageVo vo = ObjectConverter.convert(announcement, PublishedAnnouncementPageVo.class);
        vo.setIsViewed(viewRecordSet.contains(announcement.getBid()));

        return vo;
    }

    /**
     * 转换为详情VO
     * @param announcement 公告实体
     * @param content 公告内容
     * @param userId 用户ID
     * @return 详情VO
     */
    private PublishedAnnouncementDetailVo convertToDetailVo(AnnouncementActive announcement, AnnouncementContent content, String userId) {
        PublishedAnnouncementDetailVo vo = ObjectConverter.convert(announcement, PublishedAnnouncementDetailVo.class);

        // 设置公告内容
        if (content != null) {
            vo.setContent(content.getContent());
            vo.setAttachment2(ObjectConverter.convertList(content.getAttachmentList(), PublishedAnnouncementDetailVo.AttachmentVo.class));
        }

        return vo;
    }


    private boolean checkDomain(AnnouncementActive announcement, Optional<String> domain) {
        return CollectionUtils.isEmpty(announcement.getDomain()) || (domain.isPresent() && announcement.getDomain().contains(domain.get()));
    }
}
