
package com.caidaocloud.message.service.domain.announcement.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.Data;

@Data
public class AnnouncementAttachment extends DataSimple {

    private String announcementId;

    private String name;
    private String url;

    public static String identifier = "entity.message.AnnouncementAttachment";

    public AnnouncementAttachment() {
        setIdentifier(identifier);
        setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        setCreateTime(System.currentTimeMillis());
        setCreateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
    }

    public AnnouncementAttachment(String name, String url) {
        this();
        this.name = name;
        this.url = url;
    }
}