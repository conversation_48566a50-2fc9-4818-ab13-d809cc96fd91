package com.caidaocloud.message.service.application.announcement.dto;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 已发布公告分页查询DTO
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@ApiModel(description = "已发布公告分页查询DTO")
public class PublishedAnnouncementPageDto extends BasePage {

    @ApiModelProperty(value = "公告名称（模糊查询）")
    private String name;

    @ApiModelProperty(value = "公告类型")
    private String type;

    @ApiModelProperty(value = "是否置顶")
    private Boolean isTop;

    @ApiModelProperty(value = "开始时间（发布时间范围查询）")
    private Long startTime;

    @ApiModelProperty(value = "结束时间（发布时间范围查询）")
    private Long endTime;

    @ApiModelProperty(value = "当前用户ID（用于查询查看记录）")
    private String userId;

}
