package com.caidaocloud.message.service.infrastructure.repository.impl.announcement;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeParent;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataJoin;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.message.service.application.announcement.dto.AnnouncementActivePageDto;
import com.caidaocloud.message.service.application.feign.MasterdataFeignClientV2;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementActive;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementContent;
import com.caidaocloud.message.service.domain.announcement.entity.AnnouncementReceive;
import com.caidaocloud.message.service.domain.announcement.enums.ReceiveType;
import com.caidaocloud.message.service.domain.announcement.repository.AnnouncementActiveRepository;
import com.caidaocloud.message.service.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.message.service.interfaces.dto.base.UserDetailVo;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 已生效公告Repository实现类
 *
 * <AUTHOR> Zhou
 * @date 2025/7/11
 */
@Repository
public class AnnouncementActiveRepositoryImpl extends BaseRepositoryImpl<AnnouncementActive>
        implements AnnouncementActiveRepository {
    @Autowired
    private MasterdataFeignClientV2 masterdataFeignClientV2;

    @Override
    public AnnouncementActive insert(AnnouncementActive data) {
        String dataId = DataInsert.identifier(AnnouncementActive.identifier).insert(data);
        data.setBid(dataId);
        return data;
    }

    @Override
    public AnnouncementActive findByOriginalAnnouncementId(String originalAnnouncementId) {
        DataFilter filter = getBaseFilter()
                .andEq("bid", originalAnnouncementId);

        PageResult<AnnouncementActive> result = DataQuery.identifier(AnnouncementActive.identifier)
                .decrypt()
                .specifyLanguage()
                .queryInvisible()
                .filter(filter, AnnouncementActive.class);

        if (result != null && result.getItems() != null && !result.getItems().isEmpty()) {
            return result.getItems().get(0);
        }
        return null;
    }

    @Override
    public int deleteByOriginalAnnouncementId(String originalAnnouncementId) {
        DataFilter filter = getBaseFilter()
                .andEq("bid", originalAnnouncementId);

        DataDelete.identifier(AnnouncementActive.identifier).batchDelete(filter);
        return 1;
    }

    @Override
    public List<AnnouncementActive> findExpiredActiveAnnouncements(Long currentTime) {
        DataFilter filter = getBaseFilter()
                .andLe("expiryTime", String.valueOf(currentTime));

        PageResult<AnnouncementActive> result = DataQuery.identifier(AnnouncementActive.identifier)
                .decrypt()
                .specifyLanguage()
                .queryInvisible()
                .filter(filter, AnnouncementActive.class);

        return getPageList(result);
    }

    @Override
    public int batchDeleteByOriginalIds(List<String> announcementIds) {
        if (announcementIds == null || announcementIds.isEmpty()) {
            return 0;
        }

        DataFilter filter = getBaseFilter()
                .andIn("bid", announcementIds);

        DataDelete.identifier(AnnouncementActive.identifier).batchDelete(filter);
        return announcementIds.size();
    }

    @Override
    public AnnouncementActive selectById(Serializable bid) {
        DataFilter filter = getBaseFilter().andEq("bid", String.valueOf(bid));
        List<AnnouncementActive> list = DataQuery.identifier(AnnouncementActive.identifier)
                .filter(filter, AnnouncementActive.class)
                .getItems();
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 分页
     */
    @Override
    public PageResult<AnnouncementActive> selectPage(AnnouncementActivePageDto dto) {
        DataFilter filter = getBaseFilter();
        filter = filter.andEqIf("type$dictValue", dto.getType(), () -> StringUtils.isNotEmpty(dto.getType()));
        if (dto.getStartTime() != null) {
            filter = filter.andGe("effectiveTime", String.valueOf(dto.getStartTime()));
        }
        if (dto.getEndTime() != null) {
            filter = filter.andLe("effectiveTime", String.valueOf(dto.getEndTime()));
        }
        DataJoin.ModelInfo announcement = DataJoin.ModelInfo.model(AnnouncementActive.identifier,
                Lists.list("bid", "type", "receiveType", "releaseTime", "effectiveTime", "expiryTime", "isTop"), filter)
                .orderBy("is_top desc").orderBy("effective_time desc");
        DataJoin.ModelInfo receive = receiveModel();
        DataJoin join = DataJoin.joinModels(announcement, receive, DataJoin.JoinInfo.joinInfo(
                Lists.list(DataJoin.JoinPropertyInfo.joinProperty(announcement, receive, "bid", "announcementId"))));
        if (dto.getContent() != null) {
            DataJoin.ModelInfo content = DataJoin.ModelInfo.model(AnnouncementContent.identifier, Lists.list(), DataFilter.regex("content", dto.getContent()));
            join.joinModel(content, DataJoin.JoinInfo.joinInfo(Lists.list(DataJoin.JoinPropertyInfo.joinProperty(announcement, content, "bid", "content"))));
        }
        PageResult<Triple<AnnouncementActive, AnnouncementActive, AnnouncementActive>> pageResult = join
                .join(AnnouncementActive.class, System.currentTimeMillis());
        List<AnnouncementActive> list = Sequences.sequence(pageResult.getItems()).map(Triple::getLeft)
                .toList();
        return new PageResult<>(list, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());

    }

    private DataJoin.ModelInfo receiveModel() {
        DataFilter filter = DataFilter.eq("receiveType", String.valueOf(ReceiveType.ALL_PERSONNEL.getValue()));
        UserDetailVo userInfo = masterdataFeignClientV2.loadCurrentUserDetail().getData();
        if (StringUtils.isNotEmpty(userInfo.getEmpId())) {
            filter = filter.or(DataFilter.eq("receiveType", String.valueOf(ReceiveType.DESIGNATED_PERSONNEL.getValue()))
                    .andEq("receiveValue", userInfo.getEmpId()));
        }
        if (StringUtils.isNotEmpty(userInfo.getOrgId())) {
            filter = filter
                    .or(DataFilter.eq("receiveType", String.valueOf(ReceiveType.DESIGNATED_DEPARTMENT.getValue()))
                            .andEq("receiveValue", userInfo.getOrgId()));

            DataSimple org = DataQuery.identifier("entity.hr.Org").one(userInfo.getOrgId(), DataSimple.class);
            String path = ((TreeParent) org.getProperties().get("pid")).getPath();
            List<String> pids = Arrays.stream(path.split("/")).collect(Collectors.toList());
            pids.add(userInfo.getOrgId());
            filter = filter.or(
                    DataFilter.eq("receiveType", String.valueOf(ReceiveType.DESIGNATED_AND_SUB_DEPARTMENTS.getValue()))
                            .andIn("receiveValue", pids));
        }
        return DataJoin.ModelInfo.model(AnnouncementReceive.identifier, Lists.list(), filter);
    }

    @Override
    public int deleteById(Serializable id) {
        DataDelete.identifier(AnnouncementActive.identifier).softDelete(String.valueOf(id));
        return 1;
    }
}
