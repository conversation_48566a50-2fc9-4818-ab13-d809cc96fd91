package com.caidaocloud.message.service.interfaces.vo.announcement;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 已发布公告分页查询VO
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@ApiModel(description = "已发布公告分页查询VO")
public class PublishedAnnouncementPageVo {

    @ApiModelProperty(value = "公告ID")
    private String bid;

    @ApiModelProperty(value = "公告名称")
    private String name;

    @ApiModelProperty(value = "公告类型")
    private DictSimple type;

    @ApiModelProperty(value = "发布时间（Unix时间戳，毫秒）")
    private Long releaseTime;

    @ApiModelProperty(value = "是否置顶")
    private Boolean isTop;

    @ApiModelProperty(value = "是否已查看")
    private Boolean isViewed;

}
