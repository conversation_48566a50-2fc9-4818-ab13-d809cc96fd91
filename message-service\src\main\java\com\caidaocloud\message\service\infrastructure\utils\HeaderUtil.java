package com.caidaocloud.message.service.infrastructure.utils;

import java.util.Optional;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

public class HeaderUtil {

    /**
     * Retrieves the value of the specified header from the current HTTP request.
     *
     * @param headerName The name of the header to retrieve.
     * @return An Optional containing the header value if present, or empty if not found or request is unavailable.
     */
    public static Optional<String> getHeader(String headerName) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return Optional.empty();
        }
        HttpServletRequest request = attributes.getRequest();
        return Optional.ofNullable(request.getHeader(headerName));
    }
}